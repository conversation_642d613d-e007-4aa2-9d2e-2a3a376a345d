import { getURI, getUriWebSocket } from "./config/apiConfig";
import axios from "axios";

function requestTokenValidation() {
  const event = new CustomEvent("checkTokenRequest");
  window.dispatchEvent(event);
}

export async function invalidTokenValidation() {
  const tokenVer = window.localStorage.getItem("token");
  if (!tokenVer || tokenVer?.length === 0) return true;

  const response = await fetch(`${getURI()}/Auth/validateToken`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ token: tokenVer }),
  });

  if (response.ok) {
    const data = await response?.json();
    return data?.data ?? false;
  }
  return response?.ok;
}

const baseHader = {
  "Content-Type": "application/json",
};

const baseHaderToken = {
  ...baseHader,
  Authorization: `Bearer ${token()}`,
};

export function token() {
  requestTokenValidation();
  return window.localStorage.getItem("token");
}

export function MountURI(
  path,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  let url = new URL(useConfig ? path : `${getURI()}/${path}`);
  if (isFriendlyUrl) {
    url = `${url}${dataFriendlyUrl}`;
  } else if (Boolean(data) && Object.keys(data).length > 0) {
    url.search = new URLSearchParams(data).toString();
  }
  return url;
}

export function MountURIWebSocket(
  path,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  let url = new URL(useConfig ? path : `${getUriWebSocket()}`);
  if (isFriendlyUrl) {
    url = `${url}${dataFriendlyUrl}`;
  } else if (Boolean(data) && Object.keys(data).length > 0) {
    url.search = new URLSearchParams(data).toString();
  }
  return url;
}

export function LOGIN_POST(body) {
  return {
    url: MountURI("api/Auth/Login"),
    options: {
      method: "POST",
      headers: { ...baseHader },
      body: JSON.stringify(body),
    },
  };
}

export function USER_GET(data) {
  baseHaderToken.Authorization = `Bearer ${token()}`;
  return {
    url: MountURI("Employees/list", data),
    options: {
      method: "GET",
      headers: { ...baseHaderToken },
    },
  };
}

export function GET(api, id) {
  baseHaderToken.Authorization = `Bearer ${token()}`;
  return {
    url: MountURI(`${api}/${id}`),
    options: {
      method: "GET",
      headers: { ...baseHaderToken },
    },
  };
}

export async function GET_Users() {
  try {
    const response = fetch(`${getURI()}/User/AllUsers`, {
      headers: {
        Authorization: `Bearer ${token()}`,
      },
    });

    if (response.ok) {
      const data = response?.json();
      return data.data;
    } else {
      console.error("Erro:", response?.statusText);
      if (response?.status === 401 || response?.status === 409) {
        return response?.status;
      }
    }
  } catch (error) {
    console.error("Erro buscando usuários:", error);
  }
}

export async function GET_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = "",
  onlyData = true
) {
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        headers: {
          Authorization: `Bearer ${token()}`,
        },
      }
    );

    if (response.ok) {
      const contentType = response.headers.get("Content-Type");
      if (contentType === "application/pdf") {
        // Abra o PDF em uma nova guia do navegador
        const blobpdf = await response?.blob();
        return blobpdf;
      }
      if (
        contentType ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ) {
        const blob = await response?.blob();
        return blob;
      }

      const data = await response?.json();
      if (onlyData) {
        return data.data;
      } else {
        return data;
      }
    } else {
      console.log("**** Erro:", response);
      console.error("Erro:", response?.statusText);
      if (response?.status === 401 || response?.status === 409) {
        return response?.status;
      } else return response;
    }
  } catch (error) {
    console.error("Erro na busca:", error);
    return null;
  }
}

export async function GET_PDF(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        headers: {
          "Content-Type": "application/pdf",
          Authorization: `Bearer ${token()}`,
        },
      }
    );

    if (response.ok) {
      const data = await response?.json();
      return data.data;
    } else {
      console.error("Erro:", response?.statusText);
    }
  } catch (error) {
    console.error("Erro na busca:", error);
  }
}

export async function POST_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(data),
      }
    );
    const status = await response?.json();

    if (status === 401 || status === 409) {
      return status;
    }
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function POST_FILE_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token()}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    return response;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function AXIOS_POST(
  api,
  data,
  useConfig,
  isFriendlyUrl,
  dataFriendlyUrl
) {
  try {
    const response = await axios.post(
      MountURI(api, data, useConfig, isFriendlyUrl, dataFriendlyUrl),
      data,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
      }
    );

    const status = await response?.json();
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function POST_FORMDATA(
  api,
  FormData = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, null, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token()}`,
        },
        body: FormData,
      }
    );

    const contentType = response.headers.get("Content-Type");
    if (
      contentType ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      const blob = await response?.blob();
      return blob;
    }

    const status = await response?.json();
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function PUT_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, null, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(data),
      }
    );
    const status = await response?.json();
    if (status === 401 || status === 409) {
      return status;
    }
    return status;
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function DELETE_DATA(
  api,
  data = null,
  useConfig = false,
  isFriendlyUrl = false,
  dataFriendlyUrl = ""
) {
  try {
    const response = await fetch(
      MountURI(api, null, useConfig, isFriendlyUrl, dataFriendlyUrl),
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token()}`,
        },
        body: JSON.stringify(data),
      }
    );
    const result = await response?.json();
    if (result) {
      return result;
    } else {
      console.error("Erro:", response?.statusText);
      if (response?.status === 401 || response?.status === 409) {
        return response?.status;
      }
    }
  } catch (error) {
    console.error("Erro:", error);
  }
}

export async function GET_DOWNLOAD(api, options, useConfig = false) {
  fetch(MountURI(api, options, useConfig), {
    headers: {
      Authorization: `Bearer ${token()}`,
    },
  })
    .then((res) => res.blob())
    .then((blob) => {
      let file = window.URL.createObjectURL(blob);
      window.open(file);
    })
    .catch((error) => {
      console.error("Erro fetching:", error);
    });
}
