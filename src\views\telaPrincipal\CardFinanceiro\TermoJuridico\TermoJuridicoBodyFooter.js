import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { TableDatacobTermo } from "./InstallmentTablesHandler";
import { getApi, postApi, postApiQueryFile } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import {
  convertCurrencyToFloatDynamic,
  formatCurrency,
  formatDate,
  formatDateGlobaltoSimplified,
  formatToDateOnly,
} from "src/reusable/helpers";
import { toast } from "react-toastify";

const TermoJuridicoBodyFooter = ({
  onClose,
  datacobData,
  selectedProcesso,
  selectedBens,
}) => {
  const [tableDataVencidas, setTableDataVencidas] = useState([]);
  const [tableDataVincendas, setTableDataVincendas] = useState([]);
  const [grupoCotaContrato, setGrupoCotaContrato] = useState("");

  const handleTableDataVencidasChange = (tableData) => {
    setTableDataVencidas(tableData);
  };
  const handleTableDataVincendasChange = (tableData) => {
    setTableDataVincendas(tableData);
  };

  const [loading, setLoading] = useState(false);
  const [tipoTermo, setTipoTermo] = useState([]);
  const [tipoTermoSelected, setTipoTermoSelected] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [generateLoading, setGenerateLoading] = useState(false);
  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo()]);
    await getTipoTermo();
    setLoading(false);
  }, []);

  const getTipoTermo = async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTipoTermo(response);
      } else {
        setTipoTermo([]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    asyncLoadFunc();
    return () => {
      setTableDataVencidas([]);
      setTableDataVincendas([]);
    };
  }, [asyncLoadFunc]);

  const [valorVencidas, setValorVencidas] = useState(0);
  const [valorVincendas, setValorVincendas] = useState(0);
  const [multaJuros, setMultaJuros] = useState(0);
  const [diferencaParcelas, setDiferencaParcelas] = useState(0);
  const [honorarios, setHonorarios] = useState(0);
  const [honorariosPercentual, setHonorariosPercentual] = useState(0);
  const [custas, setCustas] = useState(0);
  const [total, setTotal] = useState(0);
  const [qtdParcelas, setQtdParcelas] = useState(0);
  const [dataBase, setDataBase] = useState(new Date());
  const [valorAcordado, setValorAcordado] = useState(0);
  const [jurisdicaoAtual, setJurisdicaoAtual] = useState(
    selectedProcesso?.jurisdicao_Atual
  );
  const [nrAtual, setNrAtual] = useState(
    selectedProcesso?.numero_atual_processo
  );
  const [tipoAcao, setTipoAcao] = useState(selectedProcesso?.tipo_de_Acao);
  const [descricaoVeiculo, setDescricaoVeiculo] = useState(
    selectedBens ? selectedBens?.marca + " " + selectedBens?.modelo : ""
  );

  useEffect(() => {
    const vencidas = tableDataVencidas.filter((x) => x.parcelaSelecionada);
    const vincendas = tableDataVincendas.filter((x) => x.parcelaSelecionada);
    setValorVencidas(
      vencidas?.reduce((acum, item) => acum + item.vlOriginal, 0) ?? 0
    );
    setValorVincendas(
      vincendas?.reduce((acum, item) => acum + item.vlOriginal, 0) ?? 0
    );
    setMultaJuros(
      vencidas?.reduce(
        (acum, item) => acum + item.vlMultaNegociado + item.vlJurosNegociado,
        0
      ) ??
        0 +
          vincendas?.reduce(
            (acum, item) =>
              acum + item.vlMultaNegociado + item.vlJurosNegociado,
            0
          ) ??
        0
    );
    // setHonorarios(
    //   vencidas?.reduce((acum, item) => acum + item.vlHoNegociado, 0) ??
    //     0 + vincendas?.reduce((acum, item) => acum + item.vlHoNegociado, 0) ??
    //     0
    // );
    setCustas(
      vencidas?.reduce(
        (acum, item) =>
          acum +
          item.vlDespesasNegociado +
          item.vlNotificacaoNegociado +
          item.vlTarifaNegociado,
        0
      ) ??
        0 +
          vincendas?.reduce(
            (acum, item) =>
              acum +
              item.vlDespesasNegociado +
              item.vlNotificacaoNegociado +
              item.vlTarifaNegociado,
            0
          ) ??
        0
    );
    return () => {
      setValorVencidas(0);
      setValorVincendas(0);
      setMultaJuros(0);
      setDiferencaParcelas(0);
      setHonorarios(0);
      setCustas(0);
    };
  }, [tableDataVincendas, tableDataVencidas]);

  useEffect(() => {
    const total =
      valorVencidas +
      valorVincendas +
      multaJuros +
      diferencaParcelas +
      honorarios +
      custas;
    setTotal(total);
    setValorAcordado(total);
    return () => {
      setTotal(0);
      setValorAcordado(0);
    };
  }, [
    valorVencidas,
    valorVincendas,
    multaJuros,
    diferencaParcelas,
    honorarios,
    custas,
    total,
  ]);

  const [contrato, setContrato] = useState({});
  const handleSelect = (option) => {
    setTableDataVencidas(option.parcelas.filter((x) => x.atraso > 0) ?? []);
    setTableDataVincendas(option.parcelas.filter((x) => x.atraso <= 0) ?? []);
    setGrupoCotaContrato(option.nrContrato);
    setContrato(option);
  };

  const handleQntParcelasChange = (e) => {
    if (!/^\d*$/.test(e.target.value)) return;
    const value = parseInt(e.target.value);
    setQtdParcelas(isNaN(value) ? 0 : value);
  };

  const [parcelasGeradas, setParcelasGeradas] = useState([]);
  const handleGenerateParcela = () => {
    const parcelas = [];
    const data = new Date(dataBase + " 23:00:00");
    for (let i = 1; i <= qtdParcelas; i++) {
      parcelas.push({
        numero: i,
        dtVencimento: new Date(
          i > 1 ? data.setMonth(data.getMonth() + 1) : data
        ),
        valor: valorAcordado / qtdParcelas,
      });
    }
    setParcelasGeradas(parcelas.sort((a, b) => a.numero - b.numero));
  };

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const userLogged = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const handleGenerate = async () => {
    setGenerateLoading(true);
    try {
      const response = await postApi(
        {
          idOperador: userLogged?.id,
          idFinanciado: contrato?.idFinanciado,
          idContrato: contrato?.idContrato,
          idGrupo: contrato?.idGrupo,
          crm: userLogged?.activeConnection,
          tipo: 3,
        },
        "postPedidoCartasEtermos"
      );
      if (response?.success === true && response?.data?.id !== undefined) {
        const infos = await postApi(
          {
            pedidoId: response.data.id,
            tipoTermoId: tipoTermoSelected,
            jurisdicaoAtual: jurisdicaoAtual,
            nrAtual: nrAtual,
            clientePrincipal: financiadoData?.cliente?.trim(),
            tipoAcao: tipoAcao,
            adversoPrincipal: financiadoData?.nome,
            grupoCotaContrato: grupoCotaContrato,
            nrParcelasVencidas:
              tableDataVencidas
                ?.filter((x) => x.parcelaSelecionada)
                ?.map((x) => x.nrParcela)
                ?.join(", ") ?? "",
            valorParcelasVencidas: valorVencidas,
            multaJuros: multaJuros,
            custas: custas,
            nrParcelasVincendas:
              tableDataVincendas
                ?.filter((x) => x.parcelaSelecionada)
                ?.map((x) => x.nrParcela)
                ?.join(", ") ?? "",
            valorParcelasVincendas: valorVincendas,
            honorarios: honorarios,
            total: total,
            qtdParcelasAcordadas: qtdParcelas,
            valorAcordado: valorAcordado,
            descricaoVeiculo: descricaoVeiculo,
            parcelas:
              parcelasGeradas?.map((p) => ({
                numero: p.numero,
                vencimento: formatToDateOnly(p.dtVencimento),
                valor: p.valor,
              })) ?? [],
          },
          "postTermosInfos"
        );
        if (infos?.success === true && infos?.data === true) {
          toast.success("Requisição enviada com sucesso!");
          onClose();
        } else {
          toast.error("Erro ao enviar informação da requisição!");
        }
      } else {
        toast.error("Erro ao enviar requisição!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao enviar requisição!");
    }
    setGenerateLoading(false);
  };

  const handlePreview = async () => {
    if (!contrato?.idContrato) {
      toast.error("Selecione um contrato primeiro");
      return;
    }

    setPreviewLoading(true);
    try {
      const previewResponse = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateTermoPreview",
        "",
        {
          crm: userLogged?.activeConnection,
          idGrupo: contrato?.idGrupo,
          tipoTermoId: tipoTermoSelected,
          jurisdicaoAtual: jurisdicaoAtual,
          nrAtual: nrAtual,
          clientePrincipal: financiadoData?.cliente?.trim(),
          tipoAcao: tipoAcao,
          adversoPrincipal: financiadoData?.nome,
          grupoCotaContrato: grupoCotaContrato,
          nrParcelasVencidas:
            tableDataVencidas
              ?.filter((x) => x.parcelaSelecionada)
              ?.map((x) => x.nrParcela)
              ?.join(", ") ?? "",
          valorParcelasVencidas: valorVencidas,
          multaJuros: multaJuros,
          custas: custas,
          nrParcelasVincendas:
            tableDataVincendas
              ?.filter((x) => x.parcelaSelecionada)
              ?.map((x) => x.nrParcela)
              ?.join(", ") ?? "",
          valorParcelasVincendas: valorVincendas,
          honorarios: honorarios,
          total: total,
          dataBase: dataBase,
          qtdParcelasAcordadas: qtdParcelas,
          valorAcordado: valorAcordado,
          descricaoVeiculo: descricaoVeiculo,
          parcelas:
            parcelasGeradas?.map((p) => ({
              numero: p.numero,
              vencimento: formatToDateOnly(p.dtVencimento),
              valor: p.valor,
            })) ?? [],
        }
      );

      if (previewResponse.ok) {
        const blob = await previewResponse.blob();
        if (blob.type === "application/pdf" || blob.size > 0) {
          const url = URL.createObjectURL(blob);
          setPreviewUrl(url);
          setShowPreview(true);
        } else {
          toast.error("Não foi possível gerar o preview do documento");
        }
      } else {
        toast.error("Erro ao gerar preview do documento");
      }
    } catch (error) {
      console.error("Erro ao gerar preview:", error);
      toast.error("Erro ao gerar preview do documento");
    } finally {
      setPreviewLoading(false);
    }
  };

  const handleClosePreview = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setShowPreview(false);
  };

  const handleChangeParcelas = (index, field, value) => {
    const newParcelas = [...parcelasGeradas];
    newParcelas[index][field] = value;
    setParcelasGeradas(newParcelas);
  };

  const calcHonorarios = (e) => {
    let percentual = convertCurrencyToFloatDynamic(e.target.value);
    setHonorariosPercentual(percentual);

    setHonorarios((valorVencidas + valorVincendas) * (percentual / 100));
  };
  return (
    <>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading />{" "}
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <div className="mb-4">
            <h6 className="mb-3">Selecione o tipo de termo jurídico:</h6>
            <div className="row px-4">
              {tipoTermo.map((termo, index) => (
                <div key={index} className="col-md-6 col-lg-4 mb-3">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="termoRadio"
                      id={`termoRadio${index}`}
                      value={termo.id}
                      onChange={(e) => setTipoTermoSelected(e.target.value)}
                      checked={termo.id === tipoTermoSelected}
                    />
                    <label
                      className="form-check-label"
                      htmlFor={`termoRadio${index}`}
                      style={{
                        fontSize: "13px",
                        lineHeight: "1.3",
                        cursor: "pointer",
                        fontWeight: "500",
                      }}
                    >
                      {termo.nome}
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="row mt-4">
            <div className="col-md-4">
              <label className="pt-1">Selecione o Contrato:</label>
              <Select
                className="mr-2 ml-2"
                options={datacobData}
                getOptionLabel={(option) => option?.nrContrato}
                getOptionValue={(option) => option?.nrContrato}
                onChange={(option) => handleSelect(option)}
                placeholder="Selecione"
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">
                Exibição do Contrato no Termo Jurídico:
              </label>
              <CInput
                className="mr-2 ml-2"
                value={grupoCotaContrato}
                onChange={(e) => setGrupoCotaContrato(e.target.value)}
                disabled={
                  grupoCotaContrato === "" ||
                  grupoCotaContrato === null ||
                  grupoCotaContrato === undefined
                }
              />
            </div>
          </div>

          <div className="row mt-3 px-3">
            <div className="col-md-6 m-0 p-1">
              <CLabel>Selecione as Parcelas Vencidas:</CLabel>
              <CCard>
                <CCardBody>
                  <TableDatacobTermo
                    tableData={tableDataVencidas}
                    handleTableDataChange={handleTableDataVencidasChange}
                  />
                </CCardBody>
              </CCard>
            </div>
            <div className="col-md-6 m-0 p-1">
              <CLabel>Selecione as Parcelas Vincendas:</CLabel>
              <CCard>
                <CCardBody>
                  <TableDatacobTermo
                    tableData={tableDataVincendas}
                    handleTableDataChange={handleTableDataVincendasChange}
                  />
                </CCardBody>
              </CCard>
            </div>
          </div>

          <div className="mt-0 nowrap-cell">
            <div className="row pr-4 mb-3">
              <div className="col-md-4">
                <label className="pt-1">
                  Valor Total de parcelas vencidas:
                </label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorVencidas, false)}
                  readOnly
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">Valor de parcelas vincendas:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorVincendas, false)}
                  readOnly
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">
                  Diferença de parcelas (pagas a menor):
                </label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(diferencaParcelas, false)}
                  onChange={(e) =>
                    setDiferencaParcelas(
                      convertCurrencyToFloatDynamic(e.target.value)
                    )
                  }
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
              <div className="col-md-2">
                <label className="pt-1">Honorários %:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(honorariosPercentual, false)}
                  onChange={(e) => calcHonorarios(e)}
                />
              </div>
              <div className="col-md-2">
                <label className="pt-1">Honorários:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(honorarios, false)}
                  disabled
                />
              </div>
              <div className="col-md-2">
                <label className="pt-1">Multa e Juros:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(multaJuros, false)}
                  onChange={(e) =>
                    setMultaJuros(convertCurrencyToFloatDynamic(e.target.value))
                  }
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Custas:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(custas, false)}
                  onChange={(e) =>
                    setCustas(convertCurrencyToFloatDynamic(e.target.value))
                  }
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Total:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(total, false)}
                  readOnly
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
              <div className="col-md-4">
                <label className="pt-1">Qtd de Parcelas a serem geradas:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={qtdParcelas}
                  onChange={handleQntParcelasChange}
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">Data base:</label>
                {/*<div className="mr-2 ml-2" id="custom-termo-picker">
                   <CustomDatePicker
                    selected={dataBase}
                    onChange={(e) => setDataBase(e)}

                    // minDate={new Date()}
                  />
                </div> */}
                <input
                  type="date"
                  className="form-control"
                  value={dataBase}
                  onChange={(e) => setDataBase(e.target.value)}
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">Valor acordado:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorAcordado, false)}
                  onChange={(e) =>
                    setValorAcordado(
                      convertCurrencyToFloatDynamic(e.target.value)
                    )
                  }
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
              <div className="col-md-3">
                <label className="pt-1">Jurisdição Atual:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={jurisdicaoAtual}
                  onChange={(e) => setJurisdicaoAtual(e.target.value)}
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Número Atual:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={nrAtual}
                  onChange={(e) => setNrAtual(e.target.value)}
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Tipo Ação:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={tipoAcao}
                  onChange={(e) => setTipoAcao(e.target.value)}
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Descrição Veículo:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={descricaoVeiculo}
                  onChange={(e) => setDescricaoVeiculo(e.target.value)}
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3 justify-content-end">
              <CButton
                color="info"
                className="mr-2"
                onClick={handleGenerateParcela}
              >
                Gerar Parcelas
              </CButton>
            </div>
          </div>

          <hr
            className="mt-3 mb-4"
            style={{ borderTop: "2px solid lightgray" }}
          />

          <CCard
            className="col-md-12"
            style={{ overflow: "auto", maxHeight: "200px" }}
          >
            <CCardBody>
              <div className="row">
                <div className="col-md-2">
                  <label>Número de parcelas:</label>
                </div>
                <div className="col-md-3">
                  <label>Data de vencimento:</label>
                </div>
                <div className="col-md-3">
                  <label>Valor:</label>
                </div>
              </div>
              {parcelasGeradas.map((parcela, index) => (
                <div className="row" key={index}>
                  <div className="col-md-2">
                    <span>{parcela.numero}</span>
                  </div>
                  {index > 0 && (
                    <div className="col-md-2 input-group-sm">
                      <input
                        className="form-control mt-1"
                        value={formatDateGlobaltoSimplified(
                          parcela.dtVencimento
                        )}
                        onChange={(e) =>
                          handleChangeParcelas(
                            index,
                            "dtVencimento",
                            new Date(e.target.value + " 23:00:00")
                          )
                        }
                        type="date"
                      />
                    </div>
                  )}
                  {index === 0 && (
                    <div className="col-md-2">
                      <span className="pt-1 pl-1">
                        {formatDate(parcela.dtVencimento)}
                      </span>
                    </div>
                  )}
                  <div className="col-md-2 input-group-sm">
                    <input
                      className="form-control mt-1"
                      value={formatCurrency(parcela.valor, false)}
                      onChange={(e) =>
                        handleChangeParcelas(
                          index,
                          "valor",
                          convertCurrencyToFloatDynamic(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              ))}
            </CCardBody>
          </CCard>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton
          color="success"
          className="mr-2"
          onClick={handlePreview}
          disabled={previewLoading || !tipoTermoSelected}
        >
          {previewLoading ? "Gerando Preview..." : "Preview"}
        </CButton>
        <CButton
          color="info"
          className="mr-2"
          onClick={handleGenerate}
          disabled={generateLoading || !tipoTermoSelected}
        >
          {generateLoading ? "Gerando Termo..." : "Gerar Termo"}
        </CButton>
        <CButton color="secondary" className="mr-2" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
      {/* Preview Modal */}
      {showPreview && (
        <CModal
          show={showPreview}
          onClose={handleClosePreview}
          closeOnBackdrop={false}
          className="preview-modal"
          centered
        >
          <CModalHeader closeButton>
            <h5>Preview do Termo Jurídico</h5>
          </CModalHeader>
          <CModalBody style={{ padding: "0", height: "80vh" }}>
            {previewUrl && (
              <iframe
                src={previewUrl}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                }}
                title="Preview do Termo Jurídico"
              />
            )}
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={handleClosePreview}>
              Fechar Preview
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </>
  );
};

export default TermoJuridicoBodyFooter;
