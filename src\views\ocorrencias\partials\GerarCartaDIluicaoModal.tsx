import {
  CModal,
  <PERSON>utton,
  CModal<PERSON>ooter,
  CModalHeader,
  CModalTitle,
  CInput,
  CModalBody,
} from "@coreui/react";
import React, { useState } from "react";
import { toast } from "react-toastify";
import { postApiQueryFile } from "src/reusable/functions";

type InputValues = {
  [key: number]: {
    periodoParcelas: string;
    dataRetorno: string;
  };
};

type ActiveContract = {
  numero_Contrato: string | null;
  id_Contrato: number | null;
  id_Grupo: number | null;
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  contratosAtivos: ActiveContract[] | null;
};

const GerarCartaDiluicaoModal = ({
  isOpen,
  onClose,
  contratosAtivos,
}: Props) => {
  const [selectedContracts, setSelectedContracts] = useState<string[]>([]);

  const [inputValues, setInputValues] = useState<InputValues>({});
  const handleInputChange = (
    event: React.FormEvent<HTMLInputElement>,
    idContract: number
  ) => {
    const { value } = event.currentTarget;
    const newInputValues = {
      [idContract]: {
        ...inputValues[idContract],
        [event.currentTarget.name]: value,
      },
    };

    setInputValues((prevValues) => ({
      ...prevValues,
      ...newInputValues,
    }));
  };

  const renderFieldsForSelectedContracts = (idContract: number) => {
    return (
      <div className="row ml-3 mt-2 mb-3" key={idContract}>
        <CInput
          type="text"
          name="periodoParcelas"
          placeholder="Período das parcelas diluídas"
          className="col-md-5 mr-2"
          onChange={(e) => handleInputChange(e, idContract)}
          value={inputValues[idContract]?.periodoParcelas}
        />
        <CInput
          type="text"
          name="dataRetorno"
          placeholder="Data de retorno do pagamento"
          className="col-md-5"
          onChange={(e) => handleInputChange(e, idContract)}
          value={inputValues[idContract]?.dataRetorno}
        />
      </div>
    );
  };

  const handleCheckboxChange = (event: React.FormEvent<HTMLInputElement>) => {
    const { checked, value } = event.currentTarget;
    if (checked) {
      setSelectedContracts([...selectedContracts, value]);
    } else {
      setSelectedContracts(selectedContracts.filter((item) => item !== value));
    }
  };

  const userLogged = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const handleSubmit = async () => {
    setIsLoading(true);
    for (const selected of selectedContracts) {
      try {
        const active = contratosAtivos?.find(
          (x) => x.numero_Contrato === selected
        );
        if (!active) {
          toast.error("Não foi possível gerar o documento");
          return;
        }

        const previewResponse = await postApiQueryFile(
          "postPedidoCartasEtermosGenerateCartaDiluicaoPreview",
          "",
          {
            crm: userLogged?.activeConnection,
            idGrupo: active?.id_Grupo,
            clientePrincipal: financiadoData?.cliente?.trim(),
            adversoPrincipal: financiadoData?.nome,
            grupoCotaContrato: selected,
            mesesParcelas: inputValues[active?.id_Contrato]?.periodoParcelas,
            dataRetornoPagamento: inputValues[active?.id_Contrato]?.dataRetorno,
          }
        );

        if (previewResponse.ok) {
          const blob = await previewResponse.blob();
          if (blob.type === "application/pdf" || blob.size > 0) {
            const url = URL.createObjectURL(blob);
            setPreviewUrl(url);
            setShowPreview(true);
          } else {
            toast.error("Não foi possível gerar o preview do documento");
          }
        } else {
          toast.error("Erro ao gerar preview do documento");
        }
      } catch (err) {
        console.error(err);
      }
    }
    setIsLoading(false);
  };

  const handleClosePreview = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setShowPreview(false);
  };

  return (
    <CModal
      className="modal custom-modal"
      show={isOpen}
      onClose={onClose}
      size="lg"
      closeOnBackdrop={false}
    >
      <CModalHeader>
        <CModalTitle>Gerar Carta de Diluição</CModalTitle>
      </CModalHeader>
      <div className="m-4">
        <div className="row mb-3">
          <div className="col">
            <h6 className="mb-0">Selecione os contratos desejados:</h6>
          </div>
        </div>
        <div className="col">
          {contratosAtivos?.map((item: ActiveContract, index: React.Key) => (
            <>
              <div key={index} className="row align-items-center mb-2">
                <div className="col-auto">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      name="checkboxTel"
                      id={`checkboxTel${index}`}
                      value={item?.numero_Contrato}
                      onChange={handleCheckboxChange}
                      checked={selectedContracts.includes(
                        item?.numero_Contrato
                      )}
                    />
                    <label
                      className="form-check-label"
                      htmlFor={`checkboxTel${index}`}
                    >
                      {item?.numero_Contrato}
                    </label>
                  </div>
                </div>
              </div>
              {selectedContracts.includes(item?.numero_Contrato) &&
                renderFieldsForSelectedContracts(item?.id_Contrato)}
            </>
          ))}
        </div>
      </div>
      <CModalFooter>
        <CButton color="danger" onClick={onClose}>
          Sair
        </CButton>
        <CButton color="success" onClick={handleSubmit}>
          {isLoading ? Confirmar
        </CButton>
      </CModalFooter>

      {showPreview && (
        <CModal
          show={showPreview}
          onClose={handleClosePreview}
          closeOnBackdrop={false}
          className="preview-modal"
          centered
        >
          <CModalHeader closeButton>
            <h5>Preview do Termo Jurídico</h5>
          </CModalHeader>
          <CModalBody style={{ padding: "0", height: "80vh" }}>
            {previewUrl && (
              <iframe
                src={previewUrl}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                }}
                title="Preview do Termo Jurídico"
              />
            )}
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={handleClosePreview}>
              Fechar Preview
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </CModal>
  );
};

export default GerarCartaDiluicaoModal;
